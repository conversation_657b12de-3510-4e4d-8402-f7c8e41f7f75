<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Altron</title>

  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Noto+Sans:wght@400;700&display=swap" rel="stylesheet">
  <link href="https://fonts.googleapis.com/css2?family=JetBrains+Mono&display=swap" rel="stylesheet">

  <link rel="icon" href="/src/assets/icon/icon-logo-color.svg">
  <link rel="stylesheet" href="/src/index.css" />

  <script type="module" src="/src/main.js"></script>

</head>

<body>
  <nav id="nav"></nav>
  <main>
    <section class="bg-slate-900 content-center pt-56 pb-10 text-center space-y-8">
      <div class="text-center space-y-6">
        <h2 class="text-white">深入了解我們如何在不同產業中實現<br>流程自動化、提升運營效率</h2>
        <div class="flex items-center justify-center">
          <p class="text-slate-400">若您的場景需求未列於現有應用中，歡迎</p>
          <button data-type=""
            class="btn-secondary group active:bg-slate-100">
            <p class="gradient-text transform transition-transform duration-300 group-hover:-translate-x-1 ">聯繫我們</p>
            <svg class="w-5 h-5 transform transition-transform duration-1000 group-hover:rotate-[360deg]"
              viewBox="0 0 20 20" fill="url(#grad)" xmlns="http://www.w3.org/2000/svg">
              <defs>
                <linearGradient id="grad" x1="0%" y1="0%" x2="100%" y2="100%">
                  <stop offset="0%" stop-color="#21DEFF" />
                  <stop offset="100%" stop-color="#E17DFF" />
                </linearGradient>
              </defs>
              <path d="M10 0L12.3335 7.66655L20 10L12.3335 12.3335L10 20L7.66655 12.3335L0 10L7.66655 7.66655L10 0Z"
                fill="currentColor" class="fill-[url(#grad)] group-hover: transition-colors duration-500" />
            </svg>
          </button>
        </div>
      </div>
    </section>
    <div class="relative">
      <section 
        class="save-area max-w-6xl mx-auto py-12 grid justify-center items-center grid-cols-3  lg:grid-cols-6 gap-6 z-10 relative">
        <button data-number="1" class="industries-tab focused">
          <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" fill="currentColor" class="bi bi-fire" viewBox="0 0 16 16">
          <path d="M8 16c3.314 0 6-2 6-5.5 0-1.5-.5-4-2.5-6 .25 1.5-1.25 2-1.25 2C11 4 9 .5 6 0c.357 2 .5 4-2 6-1.25 1-2 2.729-2 4.5C2 14 4.686 16 8 16m0-1c-1.657 0-3-1-3-2.75 0-.75.25-2 1.25-3C6.125 10 7 10.5 7 10.5c-.375-1.25.5-3.25 2-3.5-.179 1-.25 2 1 3 .625.5 1 1.364 1 2.25C11 14 9.657 15 8 15"/>
          </svg>
          <h5>消防搜救</h5>
        </button>
        <button data-number="2" class="industries-tab">
          <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" fill="currentColor" class="bi bi-house-fill" viewBox="0 0 16 16">
            <path d="M8.707 1.5a1 1 0 0 0-1.414 0L.646 8.146a.5.5 0 0 0 .708.708L8 2.207l6.646 6.647a.5.5 0 0 0 .708-.708L13 5.793V2.5a.5.5 0 0 0-.5-.5h-1a.5.5 0 0 0-.5.5v1.293z"/>
            <path d="m8 3.293 6 6V13.5a1.5 1.5 0 0 1-1.5 1.5h-9A1.5 1.5 0 0 1 2 13.5V9.293z"/>
          </svg>
          <h5>建築營造</h5>
        </button>
        <button data-number="3" class="industries-tab">
          <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" fill="currentColor" class="bi bi-box-seam-fill" viewBox="0 0 16 16">  <path fill-rule="evenodd" d="M15.528 2.973a.75.75 0 0 1 .472.696v8.662a.75.75 0 0 1-.472.696l-7.25 2.9a.75.75 0 0 1-.557 0l-7.25-2.9A.75.75 0 0 1 0 12.331V3.669a.75.75 0 0 1 .471-.696L7.443.184l.01-.003.268-.108a.75.75 0 0 1 .558 0l.269.108.01.003zM10.404 2 4.25 4.461 1.846 3.5 1 3.839v.4l6.5 2.6v7.922l.5.2.5-.2V6.84l6.5-2.6v-.4l-.846-.339L8 5.961 5.596 5l6.154-2.461z"/>
          </svg>
          <h5>物流倉儲</h5>
        </button>
        <button data-number="4" class="industries-tab">
          <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" fill="currentColor" class="bi bi-leaf-fill" viewBox="0 0 16 16">
            <path d="M1.4 1.7c.217.289.65.84 1.725 1.274 1.093.44 2.885.774 5.834.528 2.02-.168 3.431.51 4.326 1.556C14.161 6.082 14.5 7.41 14.5 8.5q0 .344-.027.734C13.387 8.252 11.877 7.76 10.39 7.5c-2.016-.288-4.188-.445-5.59-2.045-.142-.162-.402-.102-.379.112.108.985 1.104 1.82 1.844 2.308 2.37 1.566 5.772-.118 7.6 3.071.505.8 1.374 2.7 1.75 4.292.07.298-.066.611-.354.715a.7.7 0 0 1-.161.042 1 1 0 0 1-1.08-.794c-.13-.97-.396-1.913-.868-2.77C12.173 13.386 10.565 14 8 14c-1.854 0-3.32-.544-4.45-1.435-1.124-.887-1.889-2.095-2.39-3.383-1-2.562-1-5.536-.65-7.28L.73.806z"/>
          </svg>        
          <h5>綠色農業</h5>
        </button>
        <button data-number="5" class="industries-tab">
          <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" fill="currentColor" class="bi bi-gear-fill" viewBox="0 0 16 16">
          <path d="M9.405 1.05c-.413-1.4-2.397-1.4-2.81 0l-.1.34a1.464 1.464 0 0 1-2.105.872l-.31-.17c-1.283-.698-2.686.705-1.987 1.987l.169.311c.446.82.023 1.841-.872 2.105l-.34.1c-1.4.413-1.4 2.397 0 2.81l.34.1a1.464 1.464 0 0 1 .872 2.105l-.17.31c-.698 1.283.705 2.686 1.987 1.987l.311-.169a1.464 1.464 0 0 1 2.105.872l.1.34c.413 1.4 2.397 1.4 2.81 0l.1-.34a1.464 1.464 0 0 1 2.105-.872l.31.17c1.283.698 2.686-.705 1.987-1.987l-.169-.311a1.464 1.464 0 0 1 .872-2.105l.34-.1c1.4-.413 1.4-2.397 0-2.81l-.34-.1a1.464 1.464 0 0 1-.872-2.105l.17-.31c.698-1.283-.705-2.686-1.987-1.987l-.311.169a1.464 1.464 0 0 1-2.105-.872zM8 10.93a2.929 2.929 0 1 1 0-5.86 2.929 2.929 0 0 1 0 5.858z"/>
          </svg>
          <h5>生產製造</h5>
        </button>
        <button data-number="6" class="industries-tab">
          <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" fill="currentColor" class="bi bi-capsule" viewBox="0 0 16 16">
          <path d="M1.828 8.9 8.9 1.827a4 4 0 1 1 5.657 5.657l-7.07 7.071A4 4 0 1 1 1.827 8.9Zm9.128.771 2.893-2.893a3 3 0 1 0-4.243-4.242L6.713 5.429z"/>
          </svg>
          <h5>醫療照護</h5>
        </button>
      </section>
      <div class="w-full h-32 bg-slate-900 z-0 absolute -top-0"></div>
    </div>
    
    <!-- 各產業應用 -->
    
    <!-- industry-1 -->
    <section id="industry-1" class="industry-content mx-auto flex flex-col py-40 save-area gap-y-14">
      <!-- CAB -->
      <div class="flex flex-col-reverse md:flex-row gap-6 bg-white gap-x-10 rounded-3xl">
        <div class="basis-3/5 p-6 flex flex-col justify-between gap-y-6 min-w-0">
          <div class="space-y-4">
            <h2>CAB</h2>
            <p>
              在高風險救災環境中，CAB 機械生物採用四翅仿生機構，整合低延遲控制回路（小於10ms）與 Model Predictive Control（MPC）運動控制演算法，即使在瓦礫、碎石或坡道等不規則地形上，亦可保持穩定行進。其前方搭載的 **熱成像模組（如 FLIR Boson 640）與多氣體感測模組（NO₂、CO、CH₄）**可同步監測溫度分布與有害氣體濃度，並透過 5G 通訊模組即時傳輸至指揮中心，用於生命跡象辨識與風險區域標定。
            </p>
          </div>
          <button class="btn-primary self-start" >CAB 規格書</button>
        </div>
        <div class="md:basis-2/5 basis-full aspect-square flex-shrink">
          <img src="/src/assets/img/industries01.jpg" alt="" class="w-full lg:h-full object-cover aspect-square rounded-3xl" />
        </div>
      </div>
      <!-- HAC -->
      <div class="flex flex-col-reverse md:flex-row gap-6 bg-white gap-x-10 rounded-3xl">
        <div class="basis-3/5 p-6 flex flex-col justify-between gap-y-6 min-w-0">
          <div class="space-y-4">
            <h2>HAC</h2>
            <p>
              HAC 自主移動機器人在災難場域中，運用 3D SLAM 演算法與 LiDAR-IMU 融合定位技術（如 Cartographer + Kalman filter），可於 GPS 訊號不足處建立高解析地圖（空間誤差低於 ±2cm）。此外，內建空氣品質感測模組可即時量測懸浮微粒（PM2.5）、煙霧濃度與可燃氣體，並依據環境動態快速調整路徑規劃，支援自主避障與區域封鎖警示。
            </p>
          </div>
          <button class="btn-primary self-start" >HAC 規格書</button>
        </div>
        <div class="md:basis-2/5 basis-full aspect-square flex-shrink">
          <img src="/src/assets/img/industries02.jpg" alt="" class="w-full lg:h-full object-cover aspect-square rounded-3xl" />
        </div>
      </div>
    </section>

    <!-- industry-2 -->
    <section id="industry-2" class="industry-content mx-auto flex flex-col py-40 save-area gap-y-14">
      <!-- HRC -->
      <div class="flex flex-col-reverse md:flex-row gap-6 bg-white gap-x-10 rounded-3xl">
        <div class="basis-3/5 p-6 flex flex-col justify-between gap-y-6 min-w-0">
          <div class="space-y-4">
            <h2>HRC</h2>
            <p>
              在建築工地環境中，HRC 協作機器人具備抗灰塵與抗振動結構（符合 EN ISO 10218 標準），可部署於結構鋼構、混凝土施工等高風險環境。透過其內建的 6 軸力矩感測器與接觸式安全模組（如 DLR 柔性控制系統），可安全與工人共同執行焊接、鋼筋搬運與模板對位等作業。搭載 手臂導引與拖拽示教功能，讓非技術人員亦可快速設定路徑與動作邏輯。其 ±0.02 mm 重複定位精度，確保焊點位置準確，結構對接誤差低於業界容許範圍。
            </p>
          </div>
          <button class="btn-primary self-start" >HRC 規格書</button>
        </div>
        <div class="md:basis-2/5 basis-full aspect-square flex-shrink">
          <img src="/src/assets/img/industries03.jpg" alt="" class="w-full lg:h-full object-cover aspect-square rounded-3xl" />
        </div>
      </div>
      <!-- HAC -->
      <div class="flex flex-col-reverse md:flex-row gap-6 bg-white gap-x-10 rounded-3xl">
        <div class="basis-3/5 p-6 flex flex-col justify-between gap-y-6 min-w-0">
          <div class="space-y-4">
            <h2>HAC</h2>
            <p>
              HAC 自主移動機器人專為施工現場動態管理設計，結合 3D SLAM 與建構場感測網路（LiDAR + IMU + UWB Fusion），可在鋼骨架構、高粉塵與低照度環境中維持穩定定位。機體採用 IP65 防護等級與抗震結構，符合 EN 60204 建築工地標準。內建 360° 全景攝影與影像識別演算法，能自動記錄施工進度、辨識工安異常並推送警報至管理平台。環境感測模組支援揚塵濃度、噪音與振動監控，為施工場域提供全天候智慧巡檢支援。
            </p>
          </div>
          <button class="btn-primary self-start" >HAC 規格書</button>
        </div>
        <div class="md:basis-2/5 basis-full aspect-square flex-shrink">
          <img src="/src/assets/img/industries04.jpg" alt="" class="w-full lg:h-full object-cover aspect-square rounded-3xl" />
        </div>
      </div>
    </section>

    <!-- industry-3 -->
    <section id="industry-3" class="industry-content mx-auto flex flex-col py-40 save-area gap-y-14">
      <!-- CAB -->
      <div class="flex flex-col-reverse md:flex-row gap-6 bg-white gap-x-10 rounded-3xl">
        <div class="basis-3/5 p-6 flex flex-col justify-between gap-y-6 min-w-0">
          <div class="space-y-4">
            <h2>CAB</h2>
            <p>
              CAB 機械犬設計以高機動性支援智慧物流作業，採用四足驅動結構，整合 IMU 與 LiDAR 導航系統（如 Velodyne Puck + Bosch BNO055），具備 360° 環境感知能力，能於狹窄巷道與多層貨架間自主巡航。透過多關節動力學與 ZMP 穩定控制演算法，確保於坡道與濕滑地面依然保持平衡行走。搭載的 RFID 與 QR 掃描模組可即時確認貨品資訊，並藉由 5G 傳輸模組與中控系統同步任務狀態。單次充電續航可達 6 小時，適用於全天候倉儲巡檢與搬運支援。
            </p>
          </div>
          <button class="btn-primary self-start">CAB 規格書</button>
        </div>
        <div class="md:basis-2/5 basis-full aspect-square flex-shrink">
          <img src="/src/assets/img/industries05.jpg" alt="" class="w-full lg:h-full object-cover aspect-square rounded-3xl" />
        </div>
      </div>
      <!-- ARM -->
      <div class="flex flex-col-reverse md:flex-row gap-6 bg-white gap-x-10 rounded-3xl">
        <div class="basis-3/5 p-6 flex flex-col justify-between gap-y-6 min-w-0">
          <div class="space-y-4">
            <h2>ARM</h2>
            <p>
              ARM 機械手臂則整合 6 軸力覺感測器與 RGB-D 視覺引導系統（如 Intel RealSense D435i），能在不規則堆疊的貨品中準確抓取目標。透過 AI 分類器（基於 YOLOv8）與深度視覺資料，辨識不同尺寸、材質與條碼資訊，自動完成上架與下貨作業。伺服驅動模組支援高速抓取循環（每次取放 1.2 秒），並在 ±0.03 mm 的重複定位精度下維持作業一致性。
            </p>
          </div>
          <button class="btn-primary self-start">ARM 規格書</button>
        </div>
        <div class="md:basis-2/5 basis-full aspect-square flex-shrink">
          <img src="/src/assets/img/industries06.jpg" alt="" class="w-full lg:h-full object-cover aspect-square rounded-3xl" />
        </div>
      </div>
    </section>

    <!-- industry-4 -->
    <section id="industry-4" class="industry-content mx-auto flex flex-col py-40 save-area gap-y-14">
      <!-- HAC -->
      <div class="flex flex-col-reverse md:flex-row gap-6 bg-white gap-x-10 rounded-3xl">
        <div class="basis-3/5 p-6 flex flex-col justify-between gap-y-6 min-w-0">
          <div class="space-y-4">
            <h2>HAC</h2>
            <p>
              HAC 自主移動機器人專為智慧農場設計，採用多輪獨立驅動機構與全地形避障底盤，搭配 LiDAR-IMU 融合感測系統（如 RPLiDAR A3 + Xsens MTi-670），可於田間泥濘、斜坡或林間小徑上穩定行駛。透過 AI 深度學習模型辨識作物行列與雜草位置，自主規劃巡檢與施肥路徑。內建多點土壤感測模組可即時監測水分、pH 值與導電度，並將數據即時同步至農業管理平台。定位精度可達 ±1.5 cm，支援 RTK-GNSS 模組於開放地形中高精度導航。
            </p>
          </div>
          <button class="btn-primary self-start">HAC 規格書</button>
        </div>
        <div class="md:basis-2/5 basis-full aspect-square flex-shrink">
          <img src="/src/assets/img/industries07.jpg" alt="" class="w-full lg:h-full object-cover aspect-square rounded-3xl" />
        </div>
      </div>

      <!-- CAB -->
      <div class="flex flex-col-reverse md:flex-row gap-6 bg-white gap-x-10 rounded-3xl">
        <div class="basis-3/5 p-6 flex flex-col justify-between gap-y-6 min-w-0">
          <div class="space-y-4">
            <h2>CAB</h2>
            <p>
              CAB 蜜蜂機械生物為精準農業而設計，仿生微型飛行結構具備六自由度微調能力，整合多光譜影像感測器（如 Parrot Sequoia）與微型空氣質分析模組，可對作物進行局部授粉、病蟲害早期偵測與環境監控。搭載 AI 圖像識別模組（基於 YOLOv8 Nano）能辨識花朵成熟狀態與葉片病斑，提升作業準確率。每秒飛行調節反應時間低於 20ms，適應強風或高溫環境；電容輕量電池可支援 30 分鐘連續飛行，並支援自動充電樁返航機制。
            </p>
          </div>
          <button class="btn-primary self-start">CAB 規格書</button>
        </div>
        <div class="md:basis-2/5 basis-full aspect-square flex-shrink">
          <img src="/src/assets/img/industries08.jpg" alt="" class="w-full lg:h-full object-cover aspect-square rounded-3xl" />
        </div>
      </div>
      <!-- ARM -->
      <div class="flex flex-col-reverse md:flex-row gap-6 bg-white gap-x-10 rounded-3xl">
        <div class="basis-3/5 p-6 flex flex-col justify-between gap-y-6 min-w-0">
          <div class="space-y-4">
            <h2>ARM</h2>
            <p>
              ARM 機械手臂針對農業採收與分級作業優化設計，結合 RGB-D 相機與 3D 成像雷射（如 Intel RealSense L515 + SICK microScan3），可於複雜植株中辨識成熟果實與病變部位。AI 分類模型（基於 YOLOv8 Agri Edition）可根據果實大小、色澤與成熟度自動分級。其柔性夾爪模組配備觸覺回饋系統，降低採收損傷率。支援拖曳式示教與遠端任務派送，單次作業週期小於 1.5 秒，並具 ±0.05 mm 定位重複性，適用於多品種蔬果作業。
            </p>
          </div>
          <button class="btn-primary self-start">ARM 規格書</button>
        </div>
        <div class="md:basis-2/5 basis-full aspect-square flex-shrink">
          <img src="/src/assets/img/industries09.jpg" alt="" class="w-full lg:h-full object-cover aspect-square rounded-3xl" />
        </div>
      </div>
    </section>

    <!-- industry-5 -->
    <section id="industry-5" class="industry-content mx-auto flex flex-col py-40 save-area gap-y-14">
      <!-- ARM -->
      <div class="flex flex-col-reverse md:flex-row gap-6 bg-white gap-x-10 rounded-3xl">
        <div class="basis-3/5 p-6 flex flex-col justify-between gap-y-6 min-w-0">
          <div class="space-y-4">
            <h2>ARM</h2>
            <p>
              ARM 機械手臂則整合 結構光 3D 相機（如 Ensenso、Photoneo），提供毫米級空間深度資料，使其能在無需人工干預下完成異形件抓取與裝配作業。高達 ±0.02 mm 重複定位精度 配合視覺引導功能（Visual Servoing），實現高精度組裝與高端製造所需的嚴苛公差要求。
            </p>
          </div>
          <button class="btn-primary self-start">ARM 規格書</button>
        </div>
        <div class="md:basis-2/5 basis-full aspect-square flex-shrink">
          <img src="/src/assets/img/industries04.jpg" alt="" class="w-full lg:h-full object-cover aspect-square rounded-3xl" />
        </div>
      </div>
    </section>
    <!-- industry-6 -->
    <section id="industry-5" class="industry-content mx-auto flex flex-col py-40 save-area gap-y-14">
      <!-- HRC -->
      <div class="flex flex-col-reverse md:flex-row gap-6 bg-white gap-x-10 rounded-3xl">
        <div class="basis-3/5 p-6 flex flex-col justify-between gap-y-6 min-w-0">
          <div class="space-y-4">
            <h2>HRC</h2>
            <p>
              HRC 協作機器人專為醫療照護場域設計，採用靜音驅動模組與無菌塗層結構，符合 ISO 13485 醫療級安全規範。搭載六軸力覺感測器與視覺辨識系統，能輔助進行精準的針劑遞送、藥盒分裝與輔助照護搬運等作業。其 AI 模組可辨識病患表情與動作，並與院內 HIS 系統同步，支援自動任務排程與跨科室協同。模組化設計可快速導入不同醫療情境，如加護病房、長照中心或智慧藥局，自主動作路徑與 ±0.01 mm 重複定位精度，確保照護品質與人機安全。
            </p>
          </div>
          <button class="btn-primary self-start">HRC 規格書</button>
        </div>
        <div class="md:basis-2/5 basis-full aspect-square flex-shrink">
          <img src="/src/assets/img/industries04.jpg" alt="" class="w-full lg:h-full object-cover aspect-square rounded-3xl" />
        </div>
      </div>
    </section>
  </main>

  <div class="save-area ">
    <div class=" mx-auto text-center rounded-3xl py-40 mb-10 bg-slate-200">
      <div>
        <h3 class="pb-4">想要看看我們解決方案的實際效果嗎？</h3>
        <p class="text-center ">如果您有興趣進一步了解我們如何為您提供協助，請探索成功案例並隨時與我們聯繫。</p>
      </div>
      <div>
        <button>聯絡我們</button>
        <button>成功案例</button>
      </div>
    </div>
    
  </div>

  <footer id="footer"></footer>

  <script src="./src/js/industries.js"></script>
</body>

</html>