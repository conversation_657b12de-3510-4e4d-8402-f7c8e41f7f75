<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Altron</title>

    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans:wght@400;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=JetBrains+Mono&display=swap" rel="stylesheet">

    <link rel="icon" href="/src/assets/icon/icon-logo-color.svg">
    <link rel="stylesheet" href="/src/index.css" />

    <script type="module" src="/src/main.js"></script>
</head>

<body>
    <nav id="nav"></nav>
    <!-- Hero Section -->
    <section class="bg-slate-900 content-center pt-56 pb-20 text-center space-y-8">
        <div class="save-area flex flex-col items-center justify-center">
            <h2 class="text-white">哈囉，我們可以怎麼協助你呢？</h2>
            <div class="flex items-center justify-center">
                <p class="text-slate-400">若您的場景需求未列於現有應用中，歡迎</p>
                <button data-type=""
                class="btn-secondary group active:bg-slate-100">
                    <p class="gradient-text transform transition-transform duration-300 group-hover:-translate-x-1 ">聯繫我們</p>
                    <svg class="w-5 h-5 transform transition-transform duration-1000 group-hover:rotate-[360deg]"
                        viewBox="0 0 20 20" fill="url(#grad)" xmlns="http://www.w3.org/2000/svg">
                        <defs>
                        <linearGradient id="grad" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" stop-color="#21DEFF" />
                            <stop offset="100%" stop-color="#E17DFF" />
                        </linearGradient>
                        </defs>
                        <path d="M10 0L12.3335 7.66655L20 10L12.3335 12.3335L10 20L7.66655 12.3335L0 10L7.66655 7.66655L10 0Z"
                        fill="currentColor" class="fill-[url(#grad)] group-hover: transition-colors duration-500" />
                    </svg>
                </button>
            </div>
            
        </div>
        <div class="save-area mt-10 max-w-xl mx-auto">
            <input type="text" placeholder="搜尋支援" class="w-full px-6 py-3 rounded-full bg-white text-black placeholder-gray-400 shadow-inner" />
        </div>

    </section>

    <!-- Support Features -->
    <section class="save-area text-center flex flex-col items-center justify-center gap-20 py-20">
        <div class="space-y-4">
            <h2>全方位技術支援</h2>
            <p>專業團隊、快速反應、全天候陪伴，為您提供最強大的技術支援體驗</p>
        </div>
        <div class=" mx-auto flex flex-col lg:flex-row justify-center gap-6 ">
            <div 
            class="flex flex-col justify-between bg-white p-6 rounded-3xl text-left gap-y-6 transition-transform duration-300 hover:bg-white hover:-translate-y-2 hover:shadow-xl">
                <div class="flex flex-col gap-y-6">
                    <div class="w-16 h-16 flex items-center justify-center bg-slate-100 rounded-full">
                        <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" fill="#94a3b8" class="bi bi-envelope-paper-fill" viewBox="0 0 16 16">
                        <path fill-rule="evenodd" d="M6.5 9.5 3 7.5v-6A1.5 1.5 0 0 1 4.5 0h7A1.5 1.5 0 0 1 13 1.5v6l-3.5 2L8 8.75zM1.059 3.635 2 3.133v3.753L0 5.713V5.4a2 2 0 0 1 1.059-1.765M16 5.713l-2 1.173V3.133l.941.502A2 2 0 0 1 16 5.4zm0 1.16-5.693 3.337L16 13.372v-6.5Zm-8 3.199 7.941 4.412A2 2 0 0 1 14 16H2a2 2 0 0 1-1.941-1.516zm-8 3.3 5.693-3.162L0 6.873v6.5Z"/>
                        </svg>
                    </div>
                    <div>
                        <h4>12 小時內快速回覆</h4>
                        <p>專業技術團隊快速分析問題，提供詳細的解決方案和操作指引。</p>
                    </div>
                    <div class="flex flex-col gap-y-2">
                        <div class="flex gap-x-4">
                            <img src="/src/assets/icon/Check2Circle.svg" alt="">
                            <p>解決方案建議</p>
                        </div>
                        <div class="flex gap-x-4">
                            <img src="/src/assets/icon/Check2Circle.svg" alt="">
                            <p>操作步驟指導</p>
                        </div>
                        <div class="flex gap-x-4">
                            <img src="/src/assets/icon/Check2Circle.svg" alt="">
                            <p>服務評估分析</p>
                        </div>
                    </div>
                </div>
                <button class="btn-primary justify-center">提交問題</button>
            </div>
            <div 
            class="flex flex-col justify-between bg-white p-6 rounded-3xl text-left gap-y-6 transition-transform duration-300 hover:bg-white hover:-translate-y-2 hover:shadow-xl">

                <div class="flex flex-col gap-y-6">
                    <div class="w-16 h-16 flex items-center justify-center bg-slate-100 rounded-full">
                        <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" fill="#94a3b8" class="bi bi-headset" viewBox="0 0 16 16">
                        <path d="M8 1a5 5 0 0 0-5 5v1h1a1 1 0 0 1 1 1v3a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V6a6 6 0 1 1 12 0v6a2.5 2.5 0 0 1-2.5 2.5H9.366a1 1 0 0 1-.866.5h-1a1 1 0 1 1 0-2h1a1 1 0 0 1 .866.5H11.5A1.5 1.5 0 0 0 13 12h-1a1 1 0 0 1-1-1V8a1 1 0 0 1 1-1h1V6a5 5 0 0 0-5-5"/>
                        </svg>
                    </div>
                    <div>
                        <h4>24 小時全天候在線客服</h4>
                        <p>智能客服系統結合專業人員，隨時為您提供即時技術協助。</p>
                    </div>
                    <div class="flex flex-col gap-y-2">
                        <div class="flex gap-x-4">
                            <img src="/src/assets/icon/Check2Circle.svg" alt="">
                            <p>即時對話解答</p>
                        </div>
                        <div class="flex gap-x-4">
                            <img src="/src/assets/icon/Check2Circle.svg" alt="">
                            <p>技術支援連結</p>
                        </div>
                        <div class="flex gap-x-4">
                            <img src="/src/assets/icon/Check2Circle.svg" alt="">
                            <p>操作流程引導</p>
                        </div>
                    </div>
                </div>
                <button class="btn-primary justify-center">立即對話</button>
            </div>
            <div 
            class="flex flex-col justify-between bg-white p-6 rounded-3xl text-left gap-y-6 transition-transform duration-300 hover:bg-white hover:-translate-y-2 hover:shadow-xl">
                <div class="flex flex-col gap-y-6">
                    <div class="w-16 h-16 flex items-center justify-center bg-slate-100 rounded-full">
                    <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" fill="#94a3b8" class="bi bi-door-open-fill" viewBox="0 0 16 16">
                    <path d="M1.5 15a.5.5 0 0 0 0 1h13a.5.5 0 0 0 0-1H13V2.5A1.5 1.5 0 0 0 11.5 1H11V.5a.5.5 0 0 0-.57-.495l-7 1A.5.5 0 0 0 3 1.5V15zM11 2h.5a.5.5 0 0 1 .5.5V15h-1zm-2.5 8c-.276 0-.5-.448-.5-1s.224-1 .5-1 .5.448.5 1-.224 1-.5 1"/>
                    </svg>
                    </div>
                    <div>
                        <h4>36 小時內到達現場支援</h4>
                        <p>資深工程師現場服務，深度解決複雜技術問題和系統優化。</p>
                    </div>
                    <div class="flex flex-col gap-y-2">
                        <div class="flex gap-x-4">
                            <img src="/src/assets/icon/Check2Circle.svg" alt="">
                            <p>現場問題診斷</p>
                        </div>
                        <div class="flex gap-x-4">
                            <img src="/src/assets/icon/Check2Circle.svg" alt="">
                            <p>系統優化建議</p>
                        </div>
                        <div class="flex gap-x-4">
                            <img src="/src/assets/icon/Check2Circle.svg" alt="">
                            <p>技術維修調整</p>
                        </div>
                    </div>
                </div>
                <button class="btn-primary justify-center">預約支援</button>
            </div>
        </div>
    </section>

    <!-- 技術手冊 -->
    <section class="save-area mx-auto py-20 text-center">
        <div class=" space-y-4 mb-10">
            <h2>技術手冊</h2>
            <p>從部署建置到軟體應用，我們已為您準備好智慧化的未來藍圖</p>
        </div>

        <div class="relative h-96 flex flex-col justify-center  bg-white rounded-3xl">
            <div class="swiper tech-manual  w-full py-5 ">
                <div class="swiper-wrapper px-6">
                    <div class="swiper-slide !w-64  flex-shrink-0">
                        <div class="bg-gray-300 rounded-xl">
                            <img src="/src/assets/img/support1.jpg" alt="" class="w-full h-full object-cover rounded-xl">
                            <h5>asdasd</h5>
                        </div>
                        <button>下載</button>
                    </div>
                    <div class="swiper-slide !w-64 rounded-xl flex-shrink-0">
                        <div class="bg-gray-300 rounded-xl">
                            <img src="/src/assets/img/support1.jpg" alt="" class="w-full h-full object-cover rounded-xl">
                            <h5>asdasd</h5>
                        </div>
                        <button>下載</button>
                    </div>
                    <div class="swiper-slide  !w-64 rounded-xl flex-shrink-0">
                        <div class="bg-gray-300 rounded-xl">
                            <img src="/src/assets/img/support1.jpg" alt="" class="w-full h-full object-cover rounded-xl">
                            <h5>asdasd</h5>
                        </div>
                        <button>下載</button>
                    </div>
                    <div class="swiper-slide  !w-64  rounded-xl flex-shrink-0">
                        <img src="/src/assets/img/support1.jpg" alt="" class="w-full h-full object-cover rounded-xl">
                        <h5>asdasd</h5>
                        <button>下載</button>
                    </div>
                    <div class="swiper-slide  !w-64 rounded-xl flex-shrink-0">
                        <img src="/src/assets/img/support1.jpg" alt="" class="w-full h-full object-cover rounded-xl">
                        <h5>asdasd</h5>
                        <button>下載</button>
                    </div>
                </div>
            </div>
            <div class="swiper-pagination absolute bottom-8 right-4 z-10"></div>
        </div>
    </section>

    <!-- FAQ -->
    <section class="save-area mx-auto py-20 text-center">
        <h2 class="mb-10">常見問題</h2>
        <div class="flex flex-col gap-6 p-8 rounded-3xl bg-white text-left ">

            <div class="accordion accordion-transition space-y-4"> 
                <div class="accordion__title flex justify-between items-center cursor-pointer ">
                    <h5 class="font-bold mr-4">HAC 自主移動人形適合運用在哪些場景？</h5>
                    <h5 class="accordion__icons font-bold">
                        <span class="accordion__icon--plus">+</span>
                        <span class="accordion__icon--minus hidden">−</span>
                    </h5>
                </div>
                <div class="accordion__content hidden ">
                    <span class="border-slate-400 border-solid border-t-[1px]"></span>
                    <p>HAC 自主移動人形機器人特別適合部署於複雜且動態變化的場域，例如災難現場、農業田間、建築工地與倉儲巡檢任務。透過其先進的 3D SLAM 定位與環境感知系統，即使在 GPS 訊號微弱或地形不規則的情況下，仍能穩定行進並執行自主避障、資料蒐集與區域警示等任務。其模組化設計也便於依據任務需求進行感測器或功能擴充。</p>
                </div>
            </div>
            <div class="accordion accordion-transition space-y-4">
                <div class="accordion__title flex justify-between items-center cursor-pointer">
                    <h5 class="font-bold mr-4">ARM 機械手臂可以整合到現有的產線中嗎？</h5>
                    <h5 class="accordion__icons font-bold">
                        <span class="accordion__icon--plus">+</span>
                        <span class="accordion__icon--minus hidden">−</span>
                    </h5>
                </div>
                <div class="accordion__content  hidden">
                    <p>可以。ARM 機械手臂具備高度模組化與開放式通訊介面設計，支援與現有產線設備整合，無需大幅修改現有流程。其 AI 驅動的分類器與 RGB-D 立體視覺模組可快速辨識多樣物品，適應不規則堆疊與高變動性的物流情境。此外，其高速伺服模組與小於 1.2 秒的取放週期，能有效提升產線效率與準確率。</p>
                </div>
            </div>
            <div class="accordion accordion-transition space-y-4">                
                <div class="accordion__title flex justify-between items-center cursor-pointer">
                    <h5 class="font-bold mr-4">HRC 協作人形能處理多少公斤的負載？</h5>
                    <h5 class="accordion__icons font-bold">
                        <span class="accordion__icon--plus">+</span>
                        <span class="accordion__icon--minus hidden">−</span>
                    </h5>
                </div>
                <div class="accordion__content hidden">
                    <p>HRC 協作人形針對建築與工地場域優化設計，具備 ±0.02 mm 的高精度與符合 EN ISO 10218 安全標準的耐用結構。根據不同手臂版本與夾爪配置，最大負載能力介於 10 至 25 公斤，足以應對鋼筋搬運、焊接對位與模板安裝等常見建築任務，並可依專案需求進行客製化調整。</p>
                </div>
            </div>
            <div class="accordion accordion-transition space-y-4">
                <div class="accordion__title flex justify-between items-center cursor-pointer">
                    <h5 class="font-bold mr-4">如何獲得產品試用或方案建議？</h5>
                    <h5 class="accordion__icons font-bold">
                        <span class="accordion__icon--plus">+</span>
                        <span class="accordion__icon--minus hidden">−</span>
                    </h5>
                </div>
                <div class="accordion__content hidden">
                    <p>您可透過我們的線上諮詢表單與專屬客服團隊聯繫，我們將依據您的應用場景與技術需求，提供一對一產品展示、遠端試用支援或現場評估服務。針對企業客戶亦提供客製化整合建議與導入規劃，協助您快速落地實施。</p>
                </div>
            </div>
            <div class="accordion space-y-4">
                <div class="accordion__title flex justify-between items-center cursor-pointer">
                    <h5 class="font-bold mr-4">產品購買後，是否有中文技術支援與維修服務？</h5>
                    <h5 class="accordion__icons font-bold">
                        <span class="accordion__icon--plus">+</span>
                        <span class="accordion__icon--minus hidden">−</span>
                    </h5>                
                </div>
                <div class="accordion__content hidden ">
                    <p>有的。我們提供完整的 中文技術支援 與全台維修保固服務，涵蓋遠端診斷、韌體更新、現場維修與備品更換。專業工程團隊將依據使用情境提供操作指導與教育訓練，協助您順利完成部署並維持長期穩定運作。</p>
                </div>
                
            </div>
        </div>
    </section>
    <footer id="footer"></footer>

    <script src="./src/js/support.js"></script>

</body>

</html>