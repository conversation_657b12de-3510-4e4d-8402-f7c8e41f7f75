
<div class="w-full box-border z-[1000] fixed flex items-center justify-between px-6 top-6 ">
    <!-- logo 回首頁 -->
    <a href="/index.html" class="logo flex justify-center items-center h-12 w-12 gap-3 relative group">
        <span class="logo-nomal absolute inset-0 flex justify-center items-center ">
            <svg width="44" height="44" viewBox="0 0 29 30" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M11.4248 17.5186C13.232 16.208 15.769 16.2081 17.5762 17.5186L17.6426 17.5664H17.6445L26.4453 23.9531L16.8135 29.1699C15.3844 29.9428 13.6178 29.9427 12.1904 29.1699L2.55664 23.9531L11.4248 17.5186ZM8.59863 9.16016C9.99682 10.8314 9.99682 13.1686 8.59863 14.8398L1.56348 23.2412C0.730743 22.4539 0.25 21.3889 0.25 20.2637V9.73438C0.250138 8.23032 1.10958 6.82924 2.52148 6.06445L4.91895 4.76562L8.59863 9.16016ZM26.4775 6.06348V6.06445C27.8909 6.82927 28.7499 8.23041 28.75 9.73438V20.2637C28.75 21.3893 28.2684 22.455 27.4355 23.2412L20.4014 14.8398C19.0468 13.2207 19.0043 10.9765 20.2744 9.31836L20.4014 9.16016L24.0801 4.76562L26.4775 6.06348ZM12.1885 0.830078C13.6176 0.0571523 15.3842 0.0572666 16.8115 0.830078L21.71 3.48145L17.5762 6.48145C15.769 7.79182 13.2319 7.79196 11.4248 6.48145L7.29102 3.48145L12.1885 0.830078Z" fill="white" stroke="#F1F5F9" stroke-width="0.5"/>
            </svg>
        </span>
        <span class="logo-active absolute inset-0 group-hover:flex justify-center items-center hidden">
            <svg xmlns="http://www.w3.org/2000/svg" width="44" height="44" viewBox="0 0 44 44" fill="none">
                <path d="M17.5564 10.357C20.1699 12.2807 23.8323 12.2807 26.4458 10.357L32.5906 5.83129L25.3524 1.85369C23.2812 0.715436 20.721 0.715436 18.6476 1.85369L11.4116 5.83129L17.5564 10.357Z" fill="url(#paint0_linear_820_1897)"/>
                <path d="M38.6865 9.18306L35.1307 7.22786L29.8749 13.5996C27.8392 16.0693 27.8392 19.5303 29.8749 22L39.8065 34.0399C41.1866 32.8596 42 31.1691 42 29.3693V14.6286C42 12.3857 40.7377 10.3108 38.6843 9.18306H38.6865Z" fill="url(#paint1_linear_820_1897)"/>
                <path d="M26.4458 25.2426C23.8323 23.3189 20.1699 23.3189 17.5564 25.2426L4.89794 34.567C5.0335 34.6552 5.17129 34.7392 5.31574 34.8169L18.6498 42.1463C20.721 43.2846 23.2812 43.2846 25.3546 42.1463L38.6887 34.8169C38.8309 34.7392 38.9709 34.6531 39.1065 34.567L26.448 25.2426H26.4458Z" fill="url(#paint2_linear_820_1897)"/>
                <path d="M4.19346 34.0399L14.1251 22C16.1608 19.5303 16.1608 16.0693 14.1251 13.5996L8.86927 7.22786L5.31352 9.18306C3.26229 10.3108 2 12.3857 2 14.6286V29.3693C2 31.1691 2.81338 32.8575 4.19346 34.0399Z" fill="url(#paint3_linear_820_1897)"/>
                <defs>
                  <linearGradient id="paint0_linear_820_1897" x1="2" y1="2.68011" x2="40.6167" y2="42.6562" gradientUnits="userSpaceOnUse">
                    <stop stop-color="#00D2F6"/>
                    <stop offset="0.501932" stop-color="#A099FF"/>
                    <stop offset="1" stop-color="#C57CEB"/>
                  </linearGradient>
                  <linearGradient id="paint1_linear_820_1897" x1="2" y1="2.68011" x2="40.6167" y2="42.6562" gradientUnits="userSpaceOnUse">
                    <stop stop-color="#00D2F6"/>
                    <stop offset="0.501932" stop-color="#A099FF"/>
                    <stop offset="1" stop-color="#C57CEB"/>
                  </linearGradient>
                  <linearGradient id="paint2_linear_820_1897" x1="2" y1="2.68011" x2="40.6167" y2="42.6562" gradientUnits="userSpaceOnUse">
                    <stop stop-color="#00D2F6"/>
                    <stop offset="0.501932" stop-color="#A099FF"/>
                    <stop offset="1" stop-color="#C57CEB"/>
                  </linearGradient>
                  <linearGradient id="paint3_linear_820_1897" x1="2" y1="2.68011" x2="40.6167" y2="42.6562" gradientUnits="userSpaceOnUse">
                    <stop stop-color="#00D2F6"/>
                    <stop offset="0.501932" stop-color="#A099FF"/>
                    <stop offset="1" stop-color="#C57CEB"/>
                  </linearGradient>
                </defs>
            </svg>
        </span>

    </a>

    <!-- desktop nav -->
    <div class="h-11 hidden md:flex">
        <div class="relative  justify-center items-center gap-2 flex">
            <a href="/about.html" class="nav-btn items-center justify-center px-6 text-sm  text-slate-900 hover:text-white">關於我們</a>

            <div id="product-dropdown-trigger" class="h-full ">
                <a href="#"  class="nav-btn group items-center justify-center px-6 text-sm  text-slate-900 gap-1 hover:text-white">產品
                    <svg class="w-4 h-4 text-gray-800 group-hover:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
                        <path fill-rule="evenodd" d="M18.425 10.271C19.499 8.967 18.57 7 16.88 7H7.12c-1.69 0-2.618 1.967-1.544 3.271l4.881 5.927a2 2 0 0 0 3.088 0l4.88-5.927Z" clip-rule="evenodd"/>
                    </svg>
                </a>
                <!-- 產品 下拉選單 -->
                <div id="product-dropdown-menu" class="absolute top-full w-full mt-2 left-0 bg-white rounded-3xl p-4 gap-4 z-50 hidden">
                    <a href="/products.html" class="flex flex-col gap-3 justify-center hover:bg-slate-100 p-2 rounded-md">
                        <img src="/src/assets/img/product-a.svg" alt="A" class="w-full h-12 object-contain">
                        <span class="text-sm text-center">HAC 自主移動人形</span>
                    </a>
                    <a href="/products.html" class="flex flex-col gap-3 justify-center hover:bg-slate-100 p-2 rounded-md">
                        <img src="/src/assets/img/product-b.svg" alt="B" class="w-full h-12 object-contain">
                        <span class="text-sm text-center">HRC 協作人形</span>
                    </a>
                    <a href="/products.html" class="flex flex-col gap-3 justify-center hover:bg-slate-100 p-2 rounded-md">
                        <img src="/src/assets/img/product-c.svg" alt="C" class="w-full h-12 object-contain">
                        <span class="text-sm text-center">CAB 自主移動生物</span>
                    </a>
                    <a href="/products.html" class="flex flex-col gap-3 justify-center hover:bg-slate-100 p-2 rounded-md">
                        <img src="/src/assets/img/product-d.svg" alt="D" class="w-full h-12 object-contain">
                        <span class="text-sm text-center">ARM 機械手臂</span>
                    </a>
                </div>
            </div>

            <a href="/industries.html" class="nav-btn items-center justify-center px-6 text-sm  text-slate
            -900 hover:text-white ">應用</a>
            <a href="/support.html" class="nav-btn items-center justify-center px-6 text-sm  text-slate
            -900 hover:text-white">技術支援</a>
        </div>

        <div class="flex h-11 items-center gap-2 pl-6">
            <a href="/contact.html" class="nav-btn items-center justify-center px-6 text-sm bg-slate-900 text-white">立即預約</a>
            <a href="/login.html" class="nav-btn group w-11 h-11 justify-center items-center  ">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="size-5 group-hover:text-white ">
                    <path fill-rule="evenodd" d="M7.5 6a4.5 4.5 0 1 1 9 0 4.5 4.5 0 0 1-9 0ZM3.751 20.105a8.25 8.25 0 0 1 16.498 0 .75.75 0 0 1-.437.695A18.683 18.683 0 0 1 12 22.5c-2.786 0-5.433-.608-7.812-1.7a.75.75 0 0 1-.437-.695Z" clip-rule="evenodd"/>
                </svg>
            </a>
        </div>
    </div>
    <!-- mobile nav -->
    <div class="relative flex md:hidden ">
        <!-- Toggle Button -->
        <button id="mobile-menu-btn" class="group  w-12 h-12 flex flex-col justify-around items-center bg-white rounded-full border border-slate
        -100 hover:bg-gradient-to-br from-[#21DEFF] to-[#E17DFF]">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="size-6">
                <path fill-rule="evenodd" d="M3 6.75A.75.75 0 0 1 3.75 6h16.5a.75.75 0 0 1 0 1.5H3.75A.75.75 0 0 1 3 6.75ZM3 12a.75.75 0 0 1 .75-.75h16.5a.75.75 0 0 1 0 1.5H3.75A.75.75 0 0 1 3 12Zm0 5.25a.75.75 0 0 1 .75-.75H12a.75.75 0 0 1 0 1.5H3.75a.75.75 0 0 1-.75-.75Z" clip-rule="evenodd" />
            </svg>
        </button>
    </div>
</div>
<!-- mobile menu -->
<div id="mobile-menu" class="fixed inset-0 bg-slate
-900 z-[999] hidden md:hidden">
    <div class="flex flex-col space-y-8 text-center">
        <!-- 關閉按鈕 -->
        <button id="mobile-menu-close" class="absolute top-6 right-6 text-white text-2xl">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-8 h-8">
                <path fill-rule="evenodd" d="M5.47 5.47a.75.75 0 0 1 1.06 0L12 10.94l5.47-5.47a.75.75 0 1 1 1.06 1.06L13.06 12l5.47 5.47a.75.75 0 1 1-1.06 1.06L12 13.06l-5.47 5.47a.75.75 0 0 1-1.06-1.06L10.94 12 5.47 6.53a.75.75 0 0 1 0-1.06Z" clip-rule="evenodd" />
            </svg>
        </button>

        <!-- 導航選項 -->
        <a href="/about.html" class="text-white text-2xl font-light tracking-widest hover:text-gray-300 transition-colors">關於我們</a>

        <div class="relative">
            <button id="mobile-product-toggle" class="text-white text-2xl font-light tracking-widest hover:text-gray-300 transition-colors w-full text-center py-2 flex items-center justify-center gap-2">
                產品
                <svg class="w-5 h-5 transition-transform duration-300" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                    <path fill-rule="evenodd" d="M18.425 10.271C19.499 8.967 18.57 7 16.88 7H7.12c-1.69 0-2.618 1.967-1.544 3.271l4.881 5.927a2 2 0 0 0 3.088 0l4.88-5.927Z" clip-rule="evenodd"/>
                </svg>
            </button>
            <div id="mobile-product-menu" class="hidden mt-4 space-y-3">
                <a href="/products.html" class="block text-gray-300 text-lg hover:text-white transition-colors">HAC 自主移動人形</a>
                <a href="/products.html" class="block text-gray-300 text-lg hover:text-white transition-colors">HRC 協作人形</a>
                <a href="/products.html" class="block text-gray-300 text-lg hover:text-white transition-colors">CAB 自主移動生物</a>
                <a href="/products.html" class="block text-gray-300 text-lg hover:text-white transition-colors">ARM 機械手臂</a>
            </div>
        </div>

        <a href="/industries.html" class="text-white text-2xl font-light tracking-widest hover:text-gray-300 transition-colors">應用</a>
        <a href="/support.html" class="text-white text-2xl font-light tracking-widest hover:text-gray-300 transition-colors">技術支援</a>

        <!-- 行動按鈕 -->
        <div class="flex flex-col space-y-4 mt-8">
            <a href="/contact.html" class="bg-gradient-to-br from-[#00C5E8] to-[#BD5CDB] text-white px-8 py-3 rounded-full text-lg font-light tracking-widest">立即預約</a>
            <a href="/login.html" class="text-white text-lg font-light tracking-widest hover:text-gray-300 transition-colors flex items-center justify-center gap-2">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-6 h-6">
                    <path fill-rule="evenodd" d="M7.5 6a4.5 4.5 0 1 1 9 0 4.5 4.5 0 0 1-9 0ZM3.751 20.105a8.25 8.25 0 0 1 16.498 0 .75.75 0 0 1-.437.695A18.683 18.683 0 0 1 12 22.5c-2.786 0-5.433-.608-7.812-1.7a.75.75 0 0 1-.437-.695Z" clip-rule="evenodd"/>
                </svg>
                登入
            </a>
        </div>
    </div>
</div>
